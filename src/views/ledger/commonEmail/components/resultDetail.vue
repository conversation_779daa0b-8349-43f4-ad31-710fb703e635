<template>
  <div>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>{{ resultType === "recheck" ? "复核信息" : "配置信息" }}</span>
      </div>
      <DynamicForm
        ref="baseForm"
        :config="config"
        :params="formParams"
        labelPosition="right"
        :defaultColSpan="24"
        labelWidth="150px"
        preview
      >
        <template #fileList="{item,params}">
          <FileIcons
            :list="params[item.field]"
            :isCenter="false"
            :fileOptions="{ url: 'storePath', name: 'docName' }"
          ></FileIcons>
        </template>
        <template #mailReplyContent="{item,params}">
          <div
            v-html="params[item.field]"
            style="border:1px solid #ccc"
            v-if="params[item.field]"
          ></div>
        </template>
      </DynamicForm>
    </el-card>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>{{ resultType === "recheck" ? "复核明细" : "配置明细" }}</span>
      </div>
      <vxe-grid
        :columns="columns"
        :data="tableData"
        resizable
        align="center"
        max-height="500px"
      ></vxe-grid>
    </el-card>
  </div>
</template>

<script>
import { initParams } from "@/utils/buse";
import FileIcons from "@/components/FileIcons/index.vue";
import api from "@/api/ledger/commonEmail.js";

export default {
  components: { FileIcons },
  props: {
    mailDataId: {
      type: String,
      default: "",
    },
    resultType: {
      type: String,
      default: "config",
    },
  },
  data() {
    return {
      differenceTypeOptions: [],
      formParams: {},
      columns: [
        {
          title: "运营商名称",
          field: "operationName",
          width: 120,
        },
        {
          title: "所属省市",
          field: "provinceCity",
          width: 120,
        },
        {
          title: "场站名称",
          field: "stationName",
          width: 120,
        },
        {
          title: "场站编码",
          field: "stationCode",
          width: 200,
        },
        {
          title: "活动生效日期",
          field: "activityStartDate",
          width: 180,
        },
        {
          title: "活动结束日期",
          field: "activityEndDate",
          width: 180,
        },
        {
          title: "结算比例（%）",
          field: "settlementRatio",
          width: 120,
        },
        {
          title: "分润配置（分润收入）（%）",
          field: "profitSharingIncome",
          width: 120,
        },
        {
          title: "长协（通用分润）（%）",
          field: "longTermAgreement",
          width: 120,
        },
        {
          title: "备注",
          field: "remark",
          width: 120,
        },
        {
          title: "正确站点名称",
          field: "correctStationName",
          width: 120,
        },
        {
          title: "差异类型",
          field: "differenceType",
          width: 150,
          // formatter: ({ cellValue }) => {
          //   return this.differenceTypeOptions?.find(
          //     (item) => item.dictValue === cellValue
          //   )?.dictLabel;
          // },
        },
        {
          title: "开始时间",
          field: "recordStartTime",
          width: 180,
        },
        {
          title: "结束时间",
          field: "recordEndTime",
          width: 180,
        },
        {
          title: "应收分润（%）",
          field: "expectedProfitSharing",
          width: 120,
        },
        {
          title: "实际执行分润（%）",
          field: "actualProfitSharing",
          width: 120,
        },
        { title: "原因", field: "reason", width: 150 },
        {
          title: "差异额（%）",
          field: "differenceAmount",
          width: 120,
        },
      ],
      tableData: [],
      activeTypeOptions: [],
    };
  },
  computed: {
    config() {
      // return this.resultType === "recheck"
      //   ? this.commonConfig
      //   : [...this.commonConfig, ...this.checkConfig];
      return this.commonConfig;
    },
    commonConfig() {
      return [
        // {
        //   field: "operator",
        //   title: "运营商名称：",
        // },
        // {
        //   field: "region",
        //   title: "所属省市：",
        //   preview: {
        //     formatter: (item) => {
        //       return `${item.provinceName || ""} - ${item.cityName || ""}`;
        //     },
        //   },
        // },
        {
          field: "activeType",
          title: "活动类型：",
          previewFormatter: (val) => {
            return this.activeTypeOptions?.find((x) => x.dictValue == val)
              ?.dictLabel;
          },
        },
        {
          field: "activeTypeCount",
          title: "活动次数：",
          previewFormatter: (val) => {
            return (val ?? "-") + "次";
          },
        },
        {
          field: "stationShareCount",
          title: "场站分润：",
          previewFormatter: (val) => {
            return (val ?? "-") + "次";
          },
        },
        {
          field: "configInstructions",
          title: "配置说明：",
          show: this.resultType === "config",
        },
        {
          field: "reviewInstructions",
          title: "复核说明：",
          show: this.resultType === "recheck",
        },
        {
          field: "attachmentFileList",
          title: "上传图片：",
          previewSlot: "fileList",
          defaultValue: [],
        },
      ];
    },
    checkConfig() {
      return [
        {
          field: "isReplyNew",
          title: "是否按新邮件发送：",
          previewFormatter: (val) => {
            return val === "Y" ? "是" : val === "N" ? "否" : val;
          },
        },
        {
          field: "isReplied",
          title: "是否邮件回复：",
          previewFormatter: (val) => {
            return val === "Y" ? "回复" : val === "N" ? "不回复" : val;
          },
        },
        {
          field: "mailReplyContent",
          title: "邮件回复内容：",
          previewSlot: "mailReplyContent",
        },
        {
          field: "emailSubject",
          title: "邮件主题：",
        },
        {
          field: "recipient",
          title: "收件人：",
        },
        {
          field: "ccRecipient",
          title: "抄送人：",
        },
        {
          field: "sendAttachmentFileList",
          title: "发送附件：",
          previewSlot: "fileList",
          defaultValue: [],
        },
        {
          field: "emailSendTime",
          title: "邮件发送时间：",
        },
        {
          field: "emailSendStatus",
          title: "邮件发送状态：",
        },
      ];
    },
  },
  created() {
    this.formParams = initParams(this.config);
    this.getDicts("difference_type").then((response) => {
      this.differenceTypeOptions = response.data;
    });
    this.getDicts("activity_type").then((response) => {
      this.activeTypeOptions = response.data;
    });
  },
  mounted() {
    this.loadData();
  },
  methods: {
    loadData() {
      const method =
        this.resultType === "config" ? "getConfigDetail" : "getRecheckDetail";
      console.log("loaddata", this.resultType, method);
      api[method]({ mailDataId: this.mailDataId }).then((res) => {
        this.formParams = {
          ...initParams(this.config),
          ...res.data,
        };
        this.tableData =
          this.resultType === "config"
            ? res.data?.stationConfigDetailList
            : res.data?.stationReviewDetailList;
      });
    },
  },
};
</script>

<style></style>
