<template>
  <div class="app-container">
    <el-row :gutter="10">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="mini"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptAllOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <AdvancedForm
          :config="config"
          :queryParams="queryParams"
          ref="AdvancedForm"
          showMore
          @confirm="handleQuery"
          @resetQuery="resetQuery"
          v-if="config.length"
        >
        </AdvancedForm>

        <el-card>
          <GridTable
            :columns="columns"
            :tableData="userList"
            :checkbox="true"
            :seq="true"
            :currentPage.sync="queryParams.pageNum"
            :pageSize.sync="queryParams.pageSize"
            :total.sync="total"
            @changePage="changePage"
            :loading="loading"
            :tableId="tableId"
            :batchDelete="true"
            @handleSelectionChange="handleSelectionChange"
            row-id="userId"
            :checkMethod="checkMethod"
            ref="gridTable"
          >
            <template slot="xToolbarBtn" slot-scope="{}">
              <!-- 当前页/全部页选择器 -->
              <el-select
                v-model="selectPage"
                size="mini"
                style="margin-right: 10px; width: 86px;"
              >
                <el-option label="当前页" value="1"></el-option>
                <el-option label="全部页" value="2"></el-option>
              </el-select>

              <!-- 批量配置下拉按钮 -->
              <el-dropdown
                @command="handleBatchCommand"
                style="margin-right: 10px;"
                v-if="disableStatus == 0"
              >
                <el-button type="text" size="mini">
                  批量配置<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="batchRole"
                    >批量配置角色</el-dropdown-item
                  >
                  <el-dropdown-item command="batchDept"
                    >批量配置部门</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>

              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click.stop="handleAdd"
                v-if="disableStatus == 0"
                v-hasPermi="['system:user:add']"
                >新增
              </el-button>
            </template>
            <template slot="tags" slot-scope="{ row }">
              <el-tooltip placement="top">
                <div slot="content">
                  <el-tag
                    class="tags-tooltip-item"
                    type="info"
                    :key="item"
                    v-for="item in row.roleNames"
                    >{{ item }}
                  </el-tag>
                </div>
                <div style="display:flex;justify-content: center;">
                  <el-tag
                    type="info"
                    class="tags-span"
                    v-for="(x, i) in row.roleNames"
                    :key="x"
                    v-show="i < 2"
                  >
                    {{ x }}
                  </el-tag>
                  <span
                    v-if="getArrLength(row.roleNames) > 2"
                    style="margin-right: 10px; margin-bottom: 5px; display: inline-block;"
                    >...</span
                  >
                </div>
              </el-tooltip>
            </template>
            <!-- 状态 -->
            <!-- <template slot="statusHtml" slot-scope="{ row, $index }">
              <el-switch
                v-model="row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(row)"
              ></el-switch>
            </template> -->
            <template slot="operation" slot-scope="{ row, $index }">
              <el-button
                size="large"
                type="text"
                @click.stop="handleDetail(row)"
                >查看
              </el-button>
              <el-button
                v-show="row.userId !== userId"
                size="large"
                type="text"
                @click.stop="handleUpdate(row)"
                v-hasPermi="['system:user:edit']"
                >修改
              </el-button>
              <el-button
                v-show="row.userId !== userId"
                size="large"
                type="text"
                @click.stop="handleDelete(row)"
                v-hasPermi="['system:user:remove']"
                >删除
              </el-button>
              <el-button
                size="large"
                type="text"
                @click.stop="handleResetPwd(row)"
                v-hasPermi="['system:user:resetPwd']"
                >重置
              </el-button>
              <el-button
                size="large"
                type="text"
                @click.stop="handleUnlockUser(row)"
                v-if="row.lockStatus === '1'"
                >解锁
              </el-button>
            </template>
          </GridTable>
        </el-card>
        <div style="padding-top: 15px; text-align: right" v-if="isComponent">
          <el-button type="primary" @click.stop="emitData" size="mini"
            >确定
          </el-button>
        </div>
      </el-col>
    </el-row>

    <!-- 添加或修改参数配置对话框 -->
    <div v-if="open">
      <el-dialog
        :title="title"
        :close-on-click-modal="false"
        append-to-body
        :visible.sync="open"
        width="60%"
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <LineTitle title="基本信息" fontSize="20" />
          </el-row>
          <el-row style="display: flex; flex-wrap: wrap;">
            <el-col :span="12">
              <el-form-item label="登录账号" prop="userName">
                <el-input
                  v-model="form.userName"
                  placeholder="请输入登录账号"
                  :disabled="disabledFlag"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="归属部门" prop="deptId">
                <treeselect
                  v-model="form.deptId"
                  :options="deptAllOptions"
                  placeholder="请选择归属部门"
                  :disabled="disabledFlag"
                  :normalizer="normalizeOptions"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="账户姓名" prop="nickName">
                <el-input
                  v-model="form.nickName"
                  placeholder="请输入账户姓名"
                  :disabled="disabledFlag"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号码" prop="phonenumber">
                <el-input
                  v-model="form.phonenumber"
                  placeholder="请输入手机号码"
                  maxlength="11"
                  :disabled="disabledFlag"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.userId == undefined">
              <el-form-item label="用户密码" prop="password">
                <el-input
                  maxlength="16"
                  v-model="form.password"
                  placeholder="请输入用户密码"
                  type="password"
                  auto-complete="new-password"
                  :disabled="disabledFlag"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="禅道工号" prop="chandaoJobNum">
                <el-input v-model="form.chandaoJobNum"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="状态">
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="dict in statusOptions"
                    :key="dict.dictValue"
                    :label="dict.dictValue"
                    :disabled="disabledFlag"
                    >{{ dict.dictLabel }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider class="linestyle"></el-divider>
          <el-row style="margin-top: 20px; margin-bottom: 10px">
            <LineTitle title="选择角色" fontSize="20" />
          </el-row>

          <el-form-item label="角色" prop="roleIds">
            <el-select
              v-model="form.roleIds"
              placeholder="请选择角色"
              :disabled="disabledFlag"
              multiple
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="item in roleOptions"
                :key="item.roleId"
                :label="item.roleName"
                :value="item.roleId"
                :disabled="item.status == 1"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-row style="margin-top: 20px; margin-bottom: 10px">
            <LineTitle title="数据权限" fontSize="20" />
          </el-row>

          <el-form-item label="权限范围" prop="orgNos">
            <treeselect
              v-model="form.orgNos"
              :flat="true"
              :sort-value-by="ORDER_SELECTED()"
              :default-expand-level="1"
              :options="orgTreeList"
              :disabled="disabledFlag"
              :multiple="true"
              placeholder="请选择归属部门"
              @select="handleOrgSelect"
              :normalizer="normalizeAllOptions"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click.stop="submitForm"
            v-if="!disabledFlag"
            >确 定
          </el-button>
          <el-button @click.stop="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>

    <!-- 批量配置角色弹窗 -->
    <el-dialog
      title="批量配置角色"
      :visible.sync="batchRoleDialogVisible"
      width="50%"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="batchRoleForm" label-width="80px">
        <el-form-item label="选择角色" required>
          <el-select
            v-model="batchRoleForm.roleIds"
            placeholder="请选择角色"
            multiple
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.roleId"
              :label="item.roleName"
              :value="item.roleId"
              :disabled="item.status == 1"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelBatchRole">取 消</el-button>
        <el-button type="primary" @click="confirmBatchRole">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 批量配置部门弹窗 -->
    <el-dialog
      title="批量配置部门"
      :visible.sync="batchDeptDialogVisible"
      width="50%"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="batchDeptForm" label-width="80px">
        <el-form-item label="选择部门" required>
          <treeselect
            v-model="batchDeptForm.configDeptId"
            :options="deptAllOptions"
            placeholder="请选择归属部门"
            :normalizer="normalizeOptions"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelBatchDept">取 消</el-button>
        <el-button type="primary" @click="confirmBatchDept">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
  exportUser,
  resetUserPwd,
  unlockUser,
  changeUserStatus,
  batchConfigRole,
  batchConfigDept,
} from "@/api/system/user";
import { deptTreeSelect, deptAllTreeSelect } from "@/api/system/dept";
import { allList } from "@/api/system/role";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import LineTitle from "@/components/ViewComponents/LineTitle.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { Encrypt } from "@/utils/aes";
import { ORDER_SELECTED } from "@riophae/vue-treeselect/src/constants";

export default {
  components: { Treeselect, LineTitle, GridTable, AdvancedForm },
  data() {
    const passwordValidator = (rule, value, callback) => {
      var testPassword = /^(?![0-9a-z]+$)(?![0-9A-Z]+$)(?![0-9\W]+$)(?![a-z\W]+$)(?![a-zA-Z]+$)(?![A-Z\W]+$)[a-zA-Z0-9\W_]{8,20}$/;
      if (testPassword.test(value)) {
        callback();
      } else {
        callback(
          new Error(
            "密码应由大写字母、小写字母、特殊符号和数字中的至少三种组成！且长度在8到20之间"
          )
        );
      }
    };
    const deptValidator = (rule, value, callback) => {
      console.log(value);
      if (value !== 100) {
        callback();
      } else {
        callback(new Error("请选择对应的燃气公司！"));
      }
    };
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "deptName",
        id: "deptId",
      },
      multipleSelection: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        nickName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined,
      },
      finallySearch: null,
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "登录账号不能为空", trigger: "blur" },
        ],
        nickName: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" },
        ],
        deptId: [
          { required: true, message: "归属部门不能为空", trigger: "blur" },
          { validator: deptValidator, trigger: "blur" },
        ],
        roleIds: [
          { required: true, message: "所属角色不能为空", trigger: "blur" },
        ],
        /*orgNos: [{required: true, message: '数据权限不能为空', trigger: 'blur'}],*/
        password: [
          { required: true, message: "用户密码不能为空", trigger: "blur" },
          { validator: passwordValidator, trigger: "blur" },
        ],
        phonenumber: [
          // { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
      chooseValue: "",
      deptOptions: [], //弹框-选择的tree组织
      deptAllOptions: [],
      orgTreeList: [],
      treeObj: {}, //当前要修改的已有权限
      innerVisible: false,
      tableHeight: "",
      propstree: {
        label: "label",
        children: "children",
      },
      selectDeptId: null,
      disabledFlag: false,
      defaultCheck: [],
      config: [],
      columns: [
        {
          type: "checkbox",
          fixed: "left",
          customWidth: 60,
        },
        {
          field: "userName",
          title: "登录账号",
        },
        {
          field: "nickName",
          title: "用户昵称",
        },
        {
          field: "deptName",
          title: "部门",
        },
        {
          field: "phonenumber",
          title: "手机号码",
        },
        {
          field: "roleNames",
          title: "角色",
          slots: { default: "tags" },
          showOverflow: false,
          minWidth: 250,
          customWidth: 274,
        },
        {
          field: "statusHtml",
          title: "状态",
          slots: {
            default: ({ row }) => {
              return (
                <el-switch
                  v-model={row.status}
                  active-value="0"
                  inactive-value="1"
                  onChange={() => this.handleStatusChange(row)}
                ></el-switch>
              );
            },
          },
        },
        {
          field: "createTimeValue",
          title: "创建时间",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "userTable",
      tempParams: {}, //搜索组件的参数
      disableStatus: 0,
      // 批量配置相关
      selectPage: "1", // 当前页/全部页选择
      selectedUsers: [], // 选中的用户列表
      batchRoleDialogVisible: false, // 批量配置角色弹窗
      batchDeptDialogVisible: false, // 批量配置部门弹窗
      batchRoleForm: {
        roleIds: [],
      },
      batchDeptForm: {
        configDeptId: undefined,
      },
    };
  },
  props: ["isComponent"],
  computed: {
    userId() {
      return this.$store.state.user.userId;
    },
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
    // 监听页面选择器变化
    selectPage(newVal) {
      if (newVal === "2") {
        // 选择全部页时，清空列表已勾选
        this.clearSelectedUsers();
      }
    },
  },
  created() {
    // this.getTableHeight(); todo 感觉没用
    this.getList();
    this.getRoles();
    this.getTreeselect();
    this.getOrgTreeList();
    this.getDicts("sys_normal_status").then((response) => {
      this.statusOptions = response.data;
      this.initConfig();
    });
    /*this.getConfigKey('sys.user.initPassword').then(response => {
      this.initPassword = response.data;
    });*/
  },
  methods: {
    getArrLength(arr) {
      return arr?.length || 0;
    },
    normalizeAllOptions(node) {
      // node: 原始的选项数据
      // 在这里根据需要进行选项数据的规范化操作，并返回规范化后的选项数据
      // 例如，可以将原始的选项数据转换为符合插件要求的结构
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    normalizeOptions(node) {
      // node: 原始的选项数据
      // 在这里根据需要进行选项数据的规范化操作，并返回规范化后的选项数据
      // 例如，可以将原始的选项数据转换为符合插件要求的结构
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
        isDisabled: node.status == 1,
      };
    },

    ORDER_SELECTED() {
      return ORDER_SELECTED;
    },
    //初始化
    initConfig() {
      this.config = [
        {
          key: "userName",
          title: "登录账号",
          type: "input",
          placeholder: "请输入登录账号",
        },
        {
          key: "nickName",
          title: "账户姓名",
          type: "input",
          placeholder: "请输入用户昵称",
        },
        {
          key: "phonenumber",
          title: "手机号码",
          type: "input",
          placeholder: "请输入手机号码",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          options: this.statusOptions,
          placeholder: "请选择用户状态",
        },
      ];
    },
    chooseItem(row) {
      this.treeObj = row;
      if (!!row.dataOrg) {
        this.defaultCheck = this.treeObj.dataOrg.split(",");
        for (let i = 0; i < this.defaultCheck.length; i++) {
          this.defaultCheck[i] = Number(this.defaultCheck[i]);
        }
      }
    },
    getTableHeight() {
      this.$nextTick(() => {
        let clientHeight = window.document.documentElement.clientHeight;
        let optionsHeight = window.document.querySelector(".queryParamsWrap")
          .offsetHeight;
        this.tableHeight = clientHeight - optionsHeight - 215;
      });
    },
    //选择树状
    handleCheckChange(data) {
      // console.log("data-tree",data);
    },
    btn(row, col, event) {
      row.flag = !row.flag;
      this.$refs.multipleTable.toggleRowSelection(row, row.flag);
    },
    change() {
      this.$forceUpdate();
    },
    emitData() {
      this.$emit("emitData", this.multipleSelection[0]);
    },
    /** 查询用户列表 */
    getList(params) {
      this.selectDeptId = undefined;
      let args = this._.cloneDeep(params ? params : this.queryParams);
      args.deptId = this.queryParams.deptId; //deptId是保存在queryParams里的
      this.loading = true;
      listUser(args).then((response) => {
        this.userList = response.data;
        this.userList.forEach((element) => {
          //创建时间
          this.$set(
            element,
            "createTimeValue",
            this.parseTime(element.createTime)
          );
        });

        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      deptTreeSelect({}).then((response) => {
        console.log("部门权限 树", response.data);
        this.deptOptions = response.data;
      });
    },
    getOrgTreeList() {
      deptAllTreeSelect({}).then((response) => {
        this.deptAllOptions = response.data;
      });
      deptAllTreeSelect({
        orgType: "company",
        orgNo: this.$store.getters.tenantId,
      }).then((response) => {
        this.orgTreeList = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) {
        return true;
      }
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.disableStatus = data.status;
      this.queryParams.deptId = data.deptId;
      // 确保传递pageSize和pageNum
      const params = this.tempParams ? { ...this.tempParams } : {};
      params.pageSize = this.queryParams.pageSize;
      params.pageNum = this.queryParams.pageNum;
      this.getList(params);
    },
    /** 查询角色列表 */
    async getRoles() {
      await allList().then((response) => {
        this.roleOptions = response.data;
      });
    },
    //数据范围字典
    dataFormat(row) {
      if (!!row && !!row.dataScope) {
        switch (row.dataScope) {
          case "1":
            return "所有数据权限";
          case "2":
            return "自定义数据权限";
          case "3":
            return "本部门数据权限";
          case "4":
            return "本部门及以下数据权限";
          default:
            break;
        }
      }
    },
    rolesFormat(row) {
      let arr = [];
      let dataOrg = [];
      if (!!row && !!row.dataOrg) {
        dataOrg = row.dataOrg.split(",");
        console.log("已选组织", dataOrg);
        if (dataOrg && dataOrg.length > 0) {
          dataOrg.forEach((element) => {
            let name = this.getTreeName(this.deptOptions, Number(element));
            arr.push(name);
          });
          return arr.toString();
        }
      }
    },
    getTreeName(list, id) {
      let _this = this;
      for (let i = 0; i < list.length; i++) {
        let a = list[i];
        if (a.id === id) {
          return a.label;
        } else {
          if (a.children && a.children.length > 0) {
            let res = _this.getTreeName(a.children, id);
            if (res) {
              return res;
            }
          }
        }
      }
    },
    changeDeptId(node, instanceId) {
      this.selectDeptId =
        node.orgType === "department" ? node.parentId : node.id;
      /*if (this.form.orgNos.indexOf(this.selectDeptId) < 0) {
        this.form.orgNos.push(this.selectDeptId);
      }*/
      debugger;
      this.form.orgNos = [this.selectDeptId];
    },
    // 用户状态修改
    handleStatusChange(row) {
      const text = row.status === "0" ? "启用" : "停用";
      this.$confirm(
        '确认要"' + text + '""' + row.userName + '"用户吗?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function() {
          return changeUserStatus(row.userId, row.status);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
        })
        ["catch"](function() {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.selectDeptId = undefined;
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: [],
        chandaoJobNum: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      params.pageSize = this.queryParams.pageSize;
      this.tempParams = params;
      this.getList(params);
    },
    /** 重置按钮操作 */
    resetQuery(params) {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.tempParams = params;
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.$emit("handleSelectionChange", selection);
      this.ids = selection.map((item) => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
      this.multipleSelection = selection;
      //如果选中的有自己的账号数据，则不允许修改和删除
      this.ids.forEach((item) => {
        if (item === this.userId) {
          this.single = true;
          this.multiple = true;
        }
      });
      // 批量配置功能：更新选中的用户列表
      this.selectedUsers = selection;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getTreeselect();
      this.getOrgTreeList();
      this.form.orgNos = [];
      this.disabledFlag = false;
      this.open = true;
      this.title = "添加账户";
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      const userId = row.userId || this.ids;
      this.selectDeptId = this.form.orgNos;
      getUser(userId).then((response) => {
        console.log("用户信息--", response);
        this.disabledFlag = false;
        this.form = response.data;
        this.form.postIds = response.postIds;
        let a = [];
        for (let i = 0; i < response.data.roleIds.length; i++) {
          let str = response.data.roleIds[i].toString();
          a.push(str);
        }
        this.form.roleIds = a;
        this.open = true;
        this.title = "修改账户";
        this.form.password = "";
        this.form.orgNos = Array.isArray(this.form.orgNos)
          ? this.form.orgNos.map(String)
          : [];
        console.log("当前用户 数据权限范围", this.form.orgNos);
      });
    },
    handleDetail(row) {
      this.reset();
      const userId = row.userId || this.ids;
      getUser(userId).then((response) => {
        console.log("用户信息--", response);
        this.disabledFlag = true;
        this.form = response.data;
        this.form.postIds = response.postIds;
        let a = [];
        for (let i = 0; i < response.data.roleIds.length; i++) {
          let str = response.data.roleIds[i].toString();
          a.push(str);
        }
        this.form.roleIds = a;
        this.open = true;
        this.title = "查看账户";
        this.form.password = "";
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /^(?![0-9a-z]+$)(?![0-9A-Z]+$)(?![0-9\W]+$)(?![a-z\W]+$)(?![a-zA-Z]+$)(?![A-Z\W]+$)[a-zA-Z0-9\W_]{8,20}$/,
        inputErrorMessage:
          "密码应由大写字母、小写字母、特殊符号和数字中的至少三种组成！且长度在8到20之间",
      })
        .then(({ value }) => {
          value = Encrypt(value);
          resetUserPwd(row.userId, value).then((response) => {
            if (response.code === "10000") {
              this.msgSuccess("修改成功");
            } else {
              this.msgError(response.message);
            }
          });
        })
        ["catch"](() => {});
    },
    /** 解锁用户按钮操作 */
    handleUnlockUser(row) {
      this.$confirm("此操作将解锁该用户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          unlockUser(row.userId).then((response) => {
            if (response.code === "10000") {
              this.msgSuccess("解锁成功");
              row.lockStatus = "0";
            } else {
              this.msgError(response.message);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消解锁",
          });
        });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs.form.validate((valid) => {
        if (this.form.deptId === 100) {
          this.$message.warning("请选择对应的燃气公司！");
          return;
        }
        console.log(this.form.orgNos);
        //debugger;
        if (this.selectDeptId) {
          if (!this.form.orgNos.includes(this.selectDeptId)) {
            return this.$message.warning("必须选择所在公司的数据权限");
          }
        }
        if (valid) {
          if (this.form.userId != undefined) {
            const loading = this.$loading({
              lock: true,
              text: "修改用户信息",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)",
            });

            updateUser(this.form)
              .then((response) => {
                if (response.code === "10000") {
                  this.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.msgError(response.msg);
                }
              })
              .finally(() => {
                loading.close();
              });
          } else {
            this.form.password = Encrypt(this.form.password);
            addUser(this.form).then((response) => {
              if (response.code === "10000") {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.msg);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      /* const userIds = [];
      const m = this.$refs.multipleTable.selection
      for(let i = 0; i< m.length; i++)
      {
           userIds.push(m[i].userId);
      }*/
      const userIds = [row.userId];
      this.$confirm(
        '是否确认删除用户编号为"' + userIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function() {
          return delUser(userIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        ["catch"](function() {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有用户数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function() {
          return exportUser(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        })
        ["catch"](function() {});
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum;
        this.finallySearch.pageSize = this.queryParams.pageSize;
      }
      this.getList(this.finallySearch);
    },
    //数据权限选择事件
    handleOrgSelect(node) {
      //需求：
      //1、选择一级节点勾选所有子节点；取消勾选一级节点，已选中的节点不取消勾选；
      //2、勾选所有的子节点，父节点不联动被选中
      this.$nextTick(() => {
        this.setSelectIds(node);

        //去重
        this.form.orgNos = Array.from(new Set(this.form.orgNos));

        //添加账户时form.orgNos初始时为空，修改后不会触发视图更新，需要强制更新来刷新UI；
        if (this.title == "添加账户") {
          this.$forceUpdate();
        }
      });
    },
    setSelectIds(node) {
      //将父节点的id添加到已选列表中
      this.form.orgNos.push(node.deptId);

      //有子节点，把子节点也添加到选择列表中
      if (node?.children?.length > 0) {
        node.children.forEach((child) => {
          this.setSelectIds(child);
        });
      }
    },

    // 批量配置相关方法

    /** 清空选中的用户 */
    clearSelectedUsers() {
      if (this.$refs.gridTable) {
        this.$refs.gridTable.clearTips();
      }
      this.selectedUsers = [];
    },

    /** 勾选框禁用方法 - 全部页模式时禁用勾选框 */
    checkMethod() {
      return this.selectPage === "1";
    },

    /** 处理批量配置下拉菜单命令 */
    handleBatchCommand(command) {
      if (this.selectPage === "1" && this.selectedUsers.length === 0) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }

      if (command === "batchRole") {
        this.handleBatchRole();
      } else if (command === "batchDept") {
        this.handleBatchDept();
      }
    },

    /** 批量配置角色 */
    handleBatchRole() {
      this.batchRoleForm = {
        roleIds: [],
      };
      this.batchRoleDialogVisible = true;
    },

    /** 批量配置部门 */
    handleBatchDept() {
      this.batchDeptForm = {
        configDeptId: undefined,
      };
      this.batchDeptDialogVisible = true;
    },

    /** 确认批量配置角色 */
    async confirmBatchRole() {
      if (
        !this.batchRoleForm.roleIds ||
        this.batchRoleForm.roleIds.length === 0
      ) {
        this.$message.warning("请选择角色");
        return;
      }

      try {
        // 根据YAPI文档构造请求参数
        const params = {
          roleIds: this.batchRoleForm.roleIds,
          allPageFlag: this.selectPage === "2",
          // 当前页模式：传递选中的用户ID列表
          userIds:
            this.selectPage === "1"
              ? this.selectedUsers.map((user) => user.userId)
              : [],
        };

        // 全部页模式：需要传递查询条件，后端根据条件查询所有用户进行批量配置
        if (this.selectPage === "2") {
          // 将查询条件合并到参数中，供后端使用
          Object.assign(params, this.queryParams);
        }

        await batchConfigRole(params);
        this.$message.success("批量配置角色成功");
        this.batchRoleDialogVisible = false;
        // 清空列表已勾选
        this.clearSelectedUsers();
        this.getList();
      } catch (error) {
        console.error("批量配置角色失败:", error);
        this.$message.error("批量配置角色失败，请重试");
      }
    },

    /** 确认批量配置部门 */
    async confirmBatchDept() {
      if (!this.batchDeptForm.configDeptId) {
        this.$message.warning("请选择部门");
        return;
      }

      try {
        // 根据YAPI文档构造请求参数
        const params = {
          configDeptId: this.batchDeptForm.configDeptId,
          allPageFlag: this.selectPage === "2",
          // 当前页模式：传递选中的用户ID列表
          userIds:
            this.selectPage === "1"
              ? this.selectedUsers.map((user) => user.userId)
              : [],
        };

        // 全部页模式：需要传递查询条件，后端根据条件查询所有用户进行批量配置
        if (this.selectPage === "2") {
          // 将查询条件合并到参数中，供后端使用
          Object.assign(params, this.queryParams);
        }

        await batchConfigDept(params);
        this.$message.success("批量配置部门成功");
        this.batchDeptDialogVisible = false;
        // 清空列表已勾选
        this.clearSelectedUsers();
        this.getList();
      } catch (error) {
        console.error("批量配置部门失败:", error);
        this.$message.error("批量配置部门失败，请重试");
      }
    },

    /** 取消批量配置角色 */
    cancelBatchRole() {
      this.batchRoleDialogVisible = false;
      this.batchRoleForm = {
        roleIds: [],
      };
    },

    /** 取消批量配置部门 */
    cancelBatchDept() {
      this.batchDeptDialogVisible = false;
      this.batchDeptForm = {
        configDeptId: undefined,
      };
    },
  },
};
</script>

<style>
.el-col {
  min-height: 1px;
}

.el-form-item {
  margin-bottom: 10px;
}

.titlestyle {
  font-size: 20px;
  font-weight: 800;
}

.linestyle {
  margin: 0;
}

.el-dialog {
  max-height: 80vh;
  overflow: auto;
}

.tags-tooltip-item {
  margin-right: 10px;
  margin-bottom: 5px;
  white-space: normal;
  height: auto;
  max-width: 380px;
}
.tags-span {
  margin-right: 10px;
  margin-bottom: 5px;
  display: inline-block;
  width: 100px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
