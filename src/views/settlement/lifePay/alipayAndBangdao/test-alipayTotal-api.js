// 测试支付宝汇总账单API接口
import api from '@/api/settlement/lifePay/alipayAndBangdao/alipayTotal.js';

/**
 * 测试查询接口
 */
async function testQueryList() {
  console.log('=== 测试查询接口 ===');
  try {
    const params = {
      pageNum: 1,
      pageSize: 10,
      // 可选参数
      settlementAccountPid: '',
      diffResult: '',
      settlementAccountName: '',
      billMonthStart: '',
      billMonthEnd: '',
      startImportDay: '',
      endImportDay: '',
      allPageFlag: false
    };
    
    const result = await api.list(params);
    console.log('查询结果:', result);
    
    if (result.success) {
      console.log('✅ 查询接口测试成功');
      console.log('数据条数:', result.data?.length);
      console.log('总数:', result.total);
      return result.data;
    } else {
      console.log('❌ 查询接口测试失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 查询接口测试异常:', error);
    return null;
  }
}

/**
 * 测试下拉列表接口
 */
async function testGetDropLists() {
  console.log('=== 测试下拉列表接口 ===');
  try {
    const result = await api.getDropLists();
    console.log('下拉列表结果:', result);
    
    if (result.success) {
      console.log('✅ 下拉列表接口测试成功');
      console.log('结算账号名称列表:', result.data);
      return result.data;
    } else {
      console.log('❌ 下拉列表接口测试失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 下拉列表接口测试异常:', error);
    return null;
  }
}

/**
 * 测试导出接口
 */
async function testExport() {
  console.log('=== 测试导出接口 ===');
  try {
    const params = {
      pageNum: 1,
      pageSize: 10,
      settlementAccountPid: '',
      diffResult: '',
      settlementAccountName: '',
      billMonthStart: '',
      billMonthEnd: '',
      startImportDay: '',
      endImportDay: '',
      allPageFlag: false
    };
    
    const result = await api.export(params);
    console.log('导出结果:', result);
    
    if (result.success) {
      console.log('✅ 导出接口测试成功');
      console.log('导出数据:', result.data);
      return result.data;
    } else {
      console.log('❌ 导出接口测试失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 导出接口测试异常:', error);
    return null;
  }
}

/**
 * 测试核对接口
 */
async function testCheckWithBank(testData) {
  console.log('=== 测试核对接口 ===');
  
  if (!testData || testData.length === 0) {
    console.log('⚠️ 没有测试数据，跳过核对接口测试');
    return;
  }
  
  try {
    // 取前几条数据的ID进行测试
    const idList = testData.slice(0, 2).map(item => item.bdBillSummaryId).filter(id => id);
    
    if (idList.length === 0) {
      console.log('⚠️ 没有有效的ID，跳过核对接口测试');
      return;
    }
    
    const params = {
      idList: idList,
      allPageFlag: false
    };
    
    console.log('核对参数:', params);
    const result = await api.checkWithBank(params);
    console.log('核对结果:', result);
    
    if (result.success) {
      console.log('✅ 核对接口测试成功');
      console.log('比对一致数量:', result.data?.sameNum);
      console.log('比对不一致数量:', result.data?.diffNum);
      return result.data;
    } else {
      console.log('❌ 核对接口测试失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 核对接口测试异常:', error);
    return null;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始测试支付宝汇总账单API接口...\n');
  
  // 1. 测试下拉列表接口
  await testGetDropLists();
  console.log('\n');
  
  // 2. 测试查询接口
  const testData = await testQueryList();
  console.log('\n');
  
  // 3. 测试导出接口
  await testExport();
  console.log('\n');
  
  // 4. 测试核对接口（需要有数据）
  await testCheckWithBank(testData);
  console.log('\n');
  
  console.log('🎉 所有API接口测试完成！');
}

// 导出测试函数
export {
  testQueryList,
  testGetDropLists,
  testExport,
  testCheckWithBank,
  runAllTests
};

// 如果在浏览器控制台中使用，可以直接调用
if (typeof window !== 'undefined') {
  window.testAlipayTotalAPI = {
    testQueryList,
    testGetDropLists,
    testExport,
    testCheckWithBank,
    runAllTests
  };
  
  console.log('💡 测试函数已挂载到 window.testAlipayTotalAPI');
  console.log('💡 可以在控制台中运行: window.testAlipayTotalAPI.runAllTests()');
}
