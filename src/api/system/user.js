import request from "@/utils/request";

// 查询用户列表
export function listUser(query) {
  return request({
    url: "/system/user/list",
    method: "post",
    data: query,
  });
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: "/system/user/" + userId,
    method: "get",
  });
}
// 查询用户详细
export function getUserNew(userId) {
  return request({
    url: "/system/user/getInfoNew/" + userId,
    method: "get",
  });
}

// 新增用户
export function addUser(data) {
  return request({
    url: "/system/user/addUser",
    method: "post",
    data: data,
  });
}

// 修改用户
export function updateUser(data) {
  return request({
    url: "/system/user/updateUser",
    method: "post",
    data: data,
  });
}

// 删除用户
export function delUser(data) {
  return request({
    url: "/system/user/delUser",
    method: "post",
    data: { userIds: data },
  });
}

// 导出用户
export function exportUser(query) {
  return request({
    url: "/system/user/export",
    method: "get",
    params: query,
  });
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password,
  };
  return request({
    url: "/system/user/resetPwd",
    method: "post",
    data: data,
  });
}

// 用户密码重置
export function unlockUser(userId) {
  const data = {
    userId,
  };
  return request({
    url: "/system/user/unlock",
    method: "post",
    data: data,
  });
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status,
  };
  return request({
    url: "/system/user/changeStatus",
    method: "post",
    data: data,
  });
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: "/system/user/profile",
    method: "get",
  });
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: "/system/user/profile",
    method: "put",
    data: data,
  });
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword,
  };
  return request({
    url: "/system/user/profile/updatePwd",
    method: "put",
    params: data,
  });
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: "/system/user/profile/avatar",
    method: "post",
    data: data,
  });
}

// 批量配置用户角色
export function batchConfigRole(data) {
  return request({
    url: "/common/user/batchConfigRole",
    method: "post",
    data: data,
  });
}

// 批量配置用户部门
export function batchConfigDept(data) {
  return request({
    url: "/common/user/batchConfigDept",
    method: "post",
    data: data,
  });
}
