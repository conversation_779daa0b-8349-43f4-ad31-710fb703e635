import request from "@/utils/request";

/**
 * 支付宝与邦道账单-支付宝汇总账单API接口
 * 提供支付宝汇总账单的查询、导出、核对等功能
 */
export default {
  /**
   * 分页查询支付宝汇总账单
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum] - 页码
   * @param {number} [data.pageSize] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {Array<number>} [data.idList] - 主键id列表
   * @param {string} [data.settlementAccountPid] - 结算商户pid
   * @param {string} [data.diffResult] - 比对结果 01:一致 02:不一致 03:未匹配到数据
   * @param {string} [data.settlementAccountName] - 结算账号名称
   * @param {string} [data.billMonthStart] - 账单月份开始
   * @param {string} [data.billMonthEnd] - 账单月份结束
   * @param {boolean} [data.allPageFlag=false] - 全部页标识
   * @param {string} [data.startImportDay] - 导入时间开始
   * @param {string} [data.endImportDay] - 导入时间结束
   * @param {string} [data.fieldName] - 查询字段
   * @param {string} [data.fieldValue] - 查询字段值
   * @returns {Promise<Object>} 返回分页查询结果
   */
  list(data) {
    return request({
      url: "/st/lifePay/alipayBd/summary/queryPage",
      method: "post",
      data: data,
    });
  },

  /**
   * 导出支付宝汇总账单Excel
   * @param {Object} data - 导出参数，参数结构同查询接口
   * @returns {Promise<Object>} 返回导出结果
   */
  export(data) {
    return request({
      url: "/st/lifePay/alipayBd/summary/exportExcel",
      method: "post",
      data: data,
    });
  },

  /**
   * 与银行流水核对
   * @param {Object} data - 核对参数
   * @param {Array<number>} data.idList - 主键id列表，取列表返回的bdBillSummaryId
   * @param {boolean} [data.allPageFlag=false] - 全部页标识
   * @returns {Promise<Object>} 返回核对结果
   * @returns {Object} returns.data - 核对结果数据
   * @returns {number} returns.data.sameNum - 比对一致数量
   * @returns {number} returns.data.diffNum - 比对不一致数量
   */
  checkWithBank(data) {
    return request({
      url: "/st/lifePay/alipayBd/summary/checkWithBank",
      method: "post",
      data: data,
    });
  },

  /**
   * 查询结算账号中文名称下拉列表
   * @returns {Promise<Object>} 返回下拉列表数据
   * @returns {Array<string>} returns.data - 结算账号名称列表
   */
  getDropLists(data) {
    return request({
      url: "/st/lifePay/alipayBd/summary/queryDistinctValue",
      method: "get",
      params: data,
    });
  },
};
